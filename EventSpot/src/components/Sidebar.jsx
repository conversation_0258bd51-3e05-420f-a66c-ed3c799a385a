import { NavLink } from 'react-router-dom'

function Sidebar() {
  return (
    <aside className="sidebar">
      <div className="sidebar-header">
        <h3>Admin Dashboard</h3>
      </div>
      <nav className="sidebar-nav">
        <ul>
          <li>
            <NavLink to="/dashboard">Dashboard</NavLink>
          </li>
          <li>
            <NavLink to="/dashboard/events">Events</NavLink>
          </li>
          <li>
            <NavLink to="/dashboard/events/new">Create Event</NavLink>
          </li>
          <li>
            <NavLink to="/dashboard/tickets">Tickets</NavLink>
          </li>
          <li>
            <NavLink to="/dashboard/settings">Settings</NavLink>
          </li>
        </ul>
      </nav>
    </aside>
  )
}

export default Sidebar