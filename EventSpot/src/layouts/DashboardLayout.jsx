
import React, { ReactNode } from 'react';
// import { useAuth } from '../contexts/AuthContext';
import { useNavigate, Link } from 'react-router-dom';
// import { Button } from '@/components/ui/button';
// import { toast } from 'sonner';



const DashboardLayout = ({ children }) => {
  // const { user, signOut } = useAuth();
  let user = {
    email:'<EMAIL>'
  }
  const navigate = useNavigate();
  
  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Successfully signed out");
      navigate('/');
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to sign out. Please try again.");
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/" className="flex items-center space-x-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-7 w-7 text-eventspot-purple"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M19 4H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2Z" />
                <path d="M8 2v4" />
                <path d="M16 2v4" />
                <path d="M3 10h18" />
              </svg>
              <span className="text-xl font-bold">EventSpot</span>
            </Link>
            <span className="text-gray-500">Dashboard</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-600">{user?.email}</span>
            <bu variant="outline" size="sm" onClick={handleSignOut}>
              Sign out
            </Button>
          </div>
        </div>
      </header>
      
      <div className="flex flex-1">
        <aside className="w-64 bg-white border-r border-gray-200 hidden md:block">
          <nav className="p-4 space-y-1">
            <Link to="/dashboard" className="block px-4 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium">
              Dashboard
            </Link>
            <Link to="/dashboard/events" className="block px-4 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium">
              Events
            </Link>
            <Link to="/dashboard/events/new" className="block px-4 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium">
              Create Event
            </Link>
            <Link to="/dashboard/tickets" className="block px-4 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium">
              Tickets
            </Link>
            <Link to="/dashboard/settings" className="block px-4 py-2 rounded-md hover:bg-gray-100 text-gray-700 font-medium">
              Settings
            </Link>
          </nav>
        </aside>
        
        <main className="flex-1 bg-gray-50 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
