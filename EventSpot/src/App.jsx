import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import DashboardLayout from './layouts/DashboardLayout'
import FullLayout from './layouts/FullLayout'
import Dashboard from './pages/Dashboard'
import Events from './pages/Events'
import NewEvent from './pages/NewEvent'
import Tickets from './pages/Tickets'
import Settings from './pages/Settings'
import Login from './pages/Login'
import Signup from './pages/Signup'
import Navbar from './components/Navbar'

function App() {
  return (
    <BrowserRouter>
      <Routes>
        {/* Auth routes without navbar */}
        <Route element={<FullLayout />}>
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
        </Route>
        
        {/* Dashboard routes with sidenav */}
        <Route element={<DashboardLayout />}>
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/dashboard/events" element={<Events />} />
          <Route path="/dashboard/events/new" element={<NewEvent />} />
          <Route path="/dashboard/tickets" element={<Tickets />} />
          <Route path="/dashboard/settings" element={<Settings />} />
        </Route>
        
        {/* Public routes with navbar */}
        <Route path="/" element={<><Navbar /><div>Home Page</div></>} />
        
        {/* Redirect to home for unknown routes */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </BrowserRouter>
  )
}

export default App