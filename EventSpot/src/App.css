/* Basic reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
}

/* Navbar styles */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #333;
  color: white;
}

.navbar a {
  color: white;
  text-decoration: none;
  margin-left: 1rem;
}

.navbar-brand a {
  font-size: 1.5rem;
  font-weight: bold;
}

/* Dashboard layout */
.dashboard-container {
  display: flex;
  min-height: calc(100vh - 60px);
}

.sidebar {
  width: 250px;
  background-color: #f4f4f4;
  padding: 1rem;
  border-right: 1px solid #ddd;
}

.sidebar-nav ul {
  list-style: none;
}

.sidebar-nav a {
  display: block;
  padding: 0.5rem 0;
  color: #333;
  text-decoration: none;
}

.sidebar-nav a.active {
  font-weight: bold;
  color: #0066cc;
}

.dashboard-content {
  flex: 1;
  padding: 2rem;
}

/* Auth pages */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f4f4f4;
}

.auth-form {
  background: white;
  padding: 2rem;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 400px;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
}

.form-group input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
}

button:hover {
  background-color: #0055aa;
}